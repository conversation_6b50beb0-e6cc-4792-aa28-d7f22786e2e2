import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Alert,
  Animated,
  Vibration,
} from 'react-native';
import {
  ThumbsUp,
  AlertTriangle,
  X,
  Star,
  Clock,
  MessageSquare,
  CheckCircle2,
} from 'lucide-react-native';
import Colors from '@/constants/colors';
import { typography, spacing, radius, shadows } from '@/constants/theme';
import { BaseButton } from '@/components/base/BaseButton';
import { BottomSheet } from '@/components/base/BottomSheet';
import {
  useFormulaFeedbackStore,
  type PendingFeedbackRequest,
} from '@/stores/formula-feedback-store';
import { useAuthStore } from '@/stores/auth-store';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import { logger } from '@/utils/logger';
import * as Haptics from 'expo-haptics';

interface FormulaFeedbackProps {
  visible: boolean;
  onClose: () => void;
  request: PendingFeedbackRequest;
}

const QUICK_FEEDBACK_OPTIONS = [
  {
    id: 'perfect',
    label: 'Perfecto',
    emoji: '👍',
    workedAsExpected: true,
    rating: 5,
    wouldUseAgain: true,
    color: Colors.light.success,
  },
  {
    id: 'needs_adjustments',
    label: 'Necesitó ajustes',
    emoji: '⚠️',
    workedAsExpected: false,
    rating: 3,
    wouldUseAgain: true,
    color: Colors.light.warning,
  },
  {
    id: 'didnt_work',
    label: 'No funcionó',
    emoji: '❌',
    workedAsExpected: false,
    rating: 1,
    wouldUseAgain: false,
    color: Colors.light.error,
  },
];

const SNOOZE_OPTIONS = [
  { minutes: 15, label: '15 min' },
  { minutes: 30, label: '30 min' },
  { minutes: 60, label: '1 hora' },
  { minutes: 240, label: '4 horas' },
];

export const FormulaFeedback: React.FC<FormulaFeedbackProps> = ({ visible, onClose, request }) => {
  const { addFeedback, dismissFeedbackRequest, snoozeRequest } = useFormulaFeedbackStore();
  const { user } = useAuthStore();
  const { currentSalon } = useSalonConfigStore();

  const [selectedOption, setSelectedOption] = useState<(typeof QUICK_FEEDBACK_OPTIONS)[0] | null>(
    null
  );
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [rating, setRating] = useState(0);
  const [actualResult, setActualResult] = useState('');
  const [adjustmentsMade, setAdjustmentsMade] = useState('');
  const [wouldUseAgain, setWouldUseAgain] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const fadeAnim = new Animated.Value(0);

  useEffect(() => {
    if (visible) {
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [visible, fadeAnim]);

  const handleQuickFeedback = async (option: (typeof QUICK_FEEDBACK_OPTIONS)[0]) => {
    if (!user || !currentSalon) {
      logger.error('Missing user or salon data', 'FormulaFeedback');
      return;
    }

    setIsSubmitting(true);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    try {
      await addFeedback({
        formula_id: request.formula_id,
        salon_id: currentSalon.id,
        user_id: user.id,
        service_id: request.service_id,
        worked_as_expected: option.workedAsExpected,
        rating: option.rating,
        would_use_again: option.wouldUseAgain,
        actual_result: option.id === 'perfect' ? 'Resultado exacto como esperado' : undefined,
      });

      dismissFeedbackRequest(request.id);

      // Show success feedback
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

      onClose();

      logger.info('Quick feedback submitted', 'FormulaFeedback', {
        option: option.id,
        rating: option.rating,
        formulaId: request.formula_id,
      });
    } catch (error) {
      logger.error('Failed to submit feedback', 'FormulaFeedback', { error });
      Alert.alert('Error', 'No se pudo enviar el feedback. Inténtalo de nuevo.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDetailedFeedback = (option: (typeof QUICK_FEEDBACK_OPTIONS)[0]) => {
    setSelectedOption(option);
    setRating(option.rating);
    setWouldUseAgain(option.wouldUseAgain);
    setShowDetailModal(true);
  };

  const submitDetailedFeedback = async () => {
    if (!user || !currentSalon || !selectedOption) return;

    setIsSubmitting(true);

    try {
      await addFeedback({
        formula_id: request.formula_id,
        salon_id: currentSalon.id,
        user_id: user.id,
        service_id: request.service_id,
        worked_as_expected: selectedOption.workedAsExpected,
        rating,
        would_use_again: wouldUseAgain,
        actual_result: actualResult.trim() || undefined,
        adjustments_made: adjustmentsMade.trim() || undefined,
      });

      dismissFeedbackRequest(request.id);
      setShowDetailModal(false);
      onClose();

      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

      logger.info('Detailed feedback submitted', 'FormulaFeedback', {
        rating,
        formulaId: request.formula_id,
        hasAdjustments: !!adjustmentsMade.trim(),
      });
    } catch (error) {
      logger.error('Failed to submit detailed feedback', 'FormulaFeedback', { error });
      Alert.alert('Error', 'No se pudo enviar el feedback. Inténtalo de nuevo.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSnooze = (minutes: number) => {
    snoozeRequest(request.id, minutes);
    onClose();

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    logger.info('Feedback request snoozed', 'FormulaFeedback', {
      requestId: request.id,
      minutes,
    });
  };

  const handleDismiss = () => {
    Alert.alert(
      'Descartar feedback',
      '¿Estás seguro de que no quieres dar feedback sobre esta fórmula?',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Descartar',
          style: 'destructive',
          onPress: () => {
            dismissFeedbackRequest(request.id);
            onClose();
          },
        },
      ]
    );
  };

  const StarRating = ({
    rating: currentRating,
    onRatingChange,
  }: {
    rating: number;
    onRatingChange: (rating: number) => void;
  }) => (
    <View style={styles.starContainer}>
      {[1, 2, 3, 4, 5].map(star => (
        <TouchableOpacity key={star} onPress={() => onRatingChange(star)} style={styles.starButton}>
          <Star
            size={32}
            color={star <= currentRating ? Colors.light.warning : Colors.light.grayLight}
            fill={star <= currentRating ? Colors.light.warning : 'transparent'}
          />
        </TouchableOpacity>
      ))}
    </View>
  );

  return (
    <>
      {/* Main feedback sheet */}
      <BottomSheet
        visible={visible && !showDetailModal}
        onClose={onClose}
        title="¿Cómo funcionó la fórmula?"
        height="auto"
        showCloseButton={false}
        disableSwipeToClose
      >
        <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
          {/* Client and formula info */}
          <View style={styles.infoCard}>
            <Text style={styles.clientName}>{request.client_name}</Text>
            <Text style={styles.formulaInfo}>{request.formula_summary}</Text>
          </View>

          {/* Quick feedback options */}
          <Text style={styles.sectionTitle}>Evaluación rápida</Text>
          <View style={styles.optionsContainer}>
            {QUICK_FEEDBACK_OPTIONS.map(option => (
              <View key={option.id} style={styles.optionWrapper}>
                <TouchableOpacity
                  style={[styles.quickOption, { borderColor: option.color }]}
                  onPress={() => handleQuickFeedback(option)}
                  disabled={isSubmitting}
                >
                  <Text style={styles.optionEmoji}>{option.emoji}</Text>
                  <Text style={[styles.optionLabel, { color: option.color }]}>{option.label}</Text>
                </TouchableOpacity>

                {/* Detail button for non-perfect results */}
                {option.id !== 'perfect' && (
                  <TouchableOpacity
                    style={styles.detailButton}
                    onPress={() => handleDetailedFeedback(option)}
                  >
                    <MessageSquare size={16} color={Colors.light.textSecondary} />
                    <Text style={styles.detailButtonText}>Detalles</Text>
                  </TouchableOpacity>
                )}
              </View>
            ))}
          </View>

          {/* Snooze options */}
          <View style={styles.snoozeSection}>
            <Text style={styles.snoozeTitle}>Recordar más tarde</Text>
            <View style={styles.snoozeOptions}>
              {SNOOZE_OPTIONS.map(option => (
                <TouchableOpacity
                  key={option.minutes}
                  style={styles.snoozeButton}
                  onPress={() => handleSnooze(option.minutes)}
                >
                  <Clock size={14} color={Colors.light.textSecondary} />
                  <Text style={styles.snoozeButtonText}>{option.label}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Dismiss button */}
          <TouchableOpacity style={styles.dismissButton} onPress={handleDismiss}>
            <X size={16} color={Colors.light.textSecondary} />
            <Text style={styles.dismissText}>No dar feedback</Text>
          </TouchableOpacity>
        </Animated.View>
      </BottomSheet>

      {/* Detailed feedback modal */}
      <BottomSheet
        visible={showDetailModal}
        onClose={() => setShowDetailModal(false)}
        title="Feedback detallado"
        height="full"
      >
        <View style={styles.detailContainer}>
          <Text style={styles.detailSectionTitle}>Calificación general</Text>
          <StarRating rating={rating} onRatingChange={setRating} />

          <Text style={styles.detailSectionTitle}>¿Qué resultado obtuviste?</Text>
          <TextInput
            style={styles.textInput}
            placeholder="Describe el resultado real..."
            value={actualResult}
            onChangeText={setActualResult}
            multiline
            numberOfLines={3}
            textAlignVertical="top"
          />

          {selectedOption?.id !== 'perfect' && (
            <>
              <Text style={styles.detailSectionTitle}>¿Qué ajustes hiciste?</Text>
              <TextInput
                style={styles.textInput}
                placeholder="Describe los cambios que aplicaste..."
                value={adjustmentsMade}
                onChangeText={setAdjustmentsMade}
                multiline
                numberOfLines={3}
                textAlignVertical="top"
              />
            </>
          )}

          <View style={styles.checkboxContainer}>
            <TouchableOpacity
              style={styles.checkbox}
              onPress={() => setWouldUseAgain(!wouldUseAgain)}
            >
              <CheckCircle2
                size={20}
                color={wouldUseAgain ? Colors.light.success : Colors.light.grayLight}
                fill={wouldUseAgain ? Colors.light.success : 'transparent'}
              />
              <Text style={styles.checkboxLabel}>Usaría esta fórmula otra vez</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.detailActions}>
            <BaseButton
              title="Cancelar"
              variant="ghost"
              onPress={() => setShowDetailModal(false)}
              style={styles.cancelButton}
            />
            <BaseButton
              title="Enviar feedback"
              onPress={submitDetailedFeedback}
              loading={isSubmitting}
              disabled={rating === 0}
              style={styles.submitButton}
            />
          </View>
        </View>
      </BottomSheet>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingBottom: spacing.xl,
  },
  infoCard: {
    backgroundColor: Colors.light.surface,
    padding: spacing.md,
    borderRadius: radius.md,
    marginBottom: spacing.lg,
  },
  clientName: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  formulaInfo: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    lineHeight: 18,
  },
  sectionTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.md,
  },
  optionsContainer: {
    marginBottom: spacing.xl,
  },
  optionWrapper: {
    marginBottom: spacing.md,
  },
  quickOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.lg,
    borderRadius: radius.md,
    borderWidth: 2,
    backgroundColor: Colors.light.background,
    ...shadows.sm,
  },
  optionEmoji: {
    fontSize: 24,
    marginRight: spacing.md,
  },
  optionLabel: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.medium,
    flex: 1,
  },
  detailButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: spacing.sm,
    paddingVertical: spacing.sm,
  },
  detailButtonText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginLeft: spacing.xs,
  },
  snoozeSection: {
    marginBottom: spacing.lg,
  },
  snoozeTitle: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.textSecondary,
    marginBottom: spacing.sm,
  },
  snoozeOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  snoozeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: radius.sm,
    backgroundColor: Colors.light.surface,
  },
  snoozeButtonText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginLeft: spacing.xs,
  },
  dismissButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.md,
  },
  dismissText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginLeft: spacing.xs,
  },

  // Detailed feedback styles
  detailContainer: {
    flex: 1,
    paddingBottom: spacing.xl,
  },
  detailSectionTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.md,
    marginTop: spacing.lg,
  },
  starContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: spacing.lg,
  },
  starButton: {
    padding: spacing.xs,
  },
  textInput: {
    borderWidth: 1,
    borderColor: Colors.light.border,
    borderRadius: radius.md,
    padding: spacing.md,
    fontSize: typography.sizes.base,
    color: Colors.light.text,
    backgroundColor: Colors.light.background,
    minHeight: 80,
  },
  checkboxContainer: {
    marginVertical: spacing.lg,
  },
  checkbox: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkboxLabel: {
    fontSize: typography.sizes.base,
    color: Colors.light.text,
    marginLeft: spacing.sm,
  },
  detailActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: spacing.xl,
    gap: spacing.md,
  },
  cancelButton: {
    flex: 1,
  },
  submitButton: {
    flex: 2,
  },
});

export default FormulaFeedback;

import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Star, TrendingUp, Repeat, CheckCircle } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { typography, spacing, radius, shadows } from '@/constants/theme';
import { BaseCard } from '@/components/base/BaseCard';
import { useFormulaFeedbackStore } from '@/stores/formula-feedback-store';

interface FeedbackStatsProps {
  formulaId?: string;
  showTitle?: boolean;
}

interface StatCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: React.ReactNode;
  color: string;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, subtitle, icon, color }) => (
  <View style={[styles.statCard, { borderLeftColor: color }]}>
    <View style={styles.statIcon}>{icon}</View>
    <View style={styles.statContent}>
      <Text style={styles.statValue}>{value}</Text>
      <Text style={styles.statTitle}>{title}</Text>
      {subtitle && <Text style={styles.statSubtitle}>{subtitle}</Text>}
    </View>
  </View>
);

export const FeedbackStats: React.FC<FeedbackStatsProps> = ({ formulaId, showTitle = true }) => {
  const { feedbacks, getFormulaStats } = useFormulaFeedbackStore();

  // Calculate overall stats or formula-specific stats
  const stats = formulaId ? getFormulaStats(formulaId) : calculateOverallStats(feedbacks);

  if (stats.total_uses === 0) {
    return showTitle ? (
      <BaseCard style={styles.container}>
        <Text style={styles.title}>Estadísticas de Feedback</Text>
        <View style={styles.emptyState}>
          <Star size={48} color={Colors.light.grayLight} />
          <Text style={styles.emptyText}>Aún no hay feedback registrado</Text>
          <Text style={styles.emptySubtext}>
            Las estadísticas aparecerán cuando los estilistas evalúen las fórmulas
          </Text>
        </View>
      </BaseCard>
    ) : null;
  }

  const formatRating = (rating: number) => {
    return rating === 0 ? '-' : rating.toFixed(1);
  };

  const formatPercentage = (percentage: number) => {
    return percentage === 0 ? '-' : `${Math.round(percentage)}%`;
  };

  return (
    <BaseCard style={styles.container}>
      {showTitle && <Text style={styles.title}>Estadísticas de Feedback</Text>}

      <View style={styles.statsGrid}>
        <StatCard
          title="Calificación promedio"
          value={formatRating(stats.avg_rating)}
          subtitle={`de ${stats.total_uses} usos`}
          icon={<Star size={20} color={Colors.light.warning} fill={Colors.light.warning} />}
          color={Colors.light.warning}
        />

        <StatCard
          title="Tasa de éxito"
          value={formatPercentage(stats.success_rate)}
          subtitle="funcionó como esperado"
          icon={<CheckCircle size={20} color={Colors.light.success} />}
          color={Colors.light.success}
        />

        <StatCard
          title="Reutilización"
          value={formatPercentage(stats.would_use_again_rate)}
          subtitle="usarían otra vez"
          icon={<Repeat size={20} color={Colors.light.primary} />}
          color={Colors.light.primary}
        />

        <StatCard
          title="Total usos"
          value={stats.total_uses}
          subtitle="fórmulas aplicadas"
          icon={<TrendingUp size={20} color={Colors.light.secondary} />}
          color={Colors.light.secondary}
        />
      </View>

      {/* Additional insights */}
      {stats.total_uses >= 3 && (
        <View style={styles.insights}>
          <Text style={styles.insightsTitle}>Insights</Text>
          {generateInsights(stats).map((insight, index) => (
            <View key={index} style={styles.insight}>
              <View style={[styles.insightDot, { backgroundColor: insight.color }]} />
              <Text style={styles.insightText}>{insight.text}</Text>
            </View>
          ))}
        </View>
      )}
    </BaseCard>
  );
};

/**
 * Calculate overall stats from all feedbacks
 */
function calculateOverallStats(feedbacks: any[]) {
  if (feedbacks.length === 0) {
    return {
      total_uses: 0,
      success_rate: 0,
      avg_rating: 0,
      would_use_again_rate: 0,
    };
  }

  const successCount = feedbacks.filter(f => f.worked_as_expected).length;
  const wouldUseAgainCount = feedbacks.filter(f => f.would_use_again).length;
  const avgRating = feedbacks.reduce((sum, f) => sum + f.rating, 0) / feedbacks.length;

  return {
    total_uses: feedbacks.length,
    success_rate: (successCount / feedbacks.length) * 100,
    avg_rating: Math.round(avgRating * 10) / 10,
    would_use_again_rate: (wouldUseAgainCount / feedbacks.length) * 100,
  };
}

/**
 * Generate insights based on feedback stats
 */
function generateInsights(stats: any) {
  const insights = [];

  if (stats.avg_rating >= 4.5) {
    insights.push({
      text: 'Excelente calidad de fórmulas',
      color: Colors.light.success,
    });
  } else if (stats.avg_rating < 3.0) {
    insights.push({
      text: 'Considerar revisar el proceso de formulación',
      color: Colors.light.warning,
    });
  }

  if (stats.success_rate >= 80) {
    insights.push({
      text: 'Alta tasa de éxito en resultados',
      color: Colors.light.success,
    });
  } else if (stats.success_rate < 60) {
    insights.push({
      text: 'Oportunidad de mejora en precisión',
      color: Colors.light.error,
    });
  }

  if (stats.would_use_again_rate >= 85) {
    insights.push({
      text: 'Fórmulas confiables para repetir',
      color: Colors.light.primary,
    });
  }

  if (stats.total_uses >= 10) {
    insights.push({
      text: 'Base sólida de datos para mejorar IA',
      color: Colors.light.secondary,
    });
  }

  return insights;
}

const styles = StyleSheet.create({
  container: {
    margin: spacing.md,
  },
  title: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.lg,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.md,
  },
  statCard: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: Colors.light.surface,
    padding: spacing.md,
    borderRadius: radius.md,
    borderLeftWidth: 4,
    flexDirection: 'row',
    alignItems: 'center',
    ...shadows.sm,
  },
  statIcon: {
    marginRight: spacing.sm,
  },
  statContent: {
    flex: 1,
  },
  statValue: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
    lineHeight: 24,
  },
  statTitle: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
    marginBottom: 2,
  },
  statSubtitle: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
  },
  emptyText: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.medium,
    color: Colors.light.textSecondary,
    marginTop: spacing.md,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginTop: spacing.sm,
    textAlign: 'center',
    lineHeight: 18,
  },
  insights: {
    marginTop: spacing.lg,
    paddingTop: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
  },
  insightsTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing.md,
  },
  insight: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  insightDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginRight: spacing.sm,
  },
  insightText: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    flex: 1,
    lineHeight: 18,
  },
});

export default FeedbackStats;

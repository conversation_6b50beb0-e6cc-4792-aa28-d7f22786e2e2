import React, { useState, useEffect, useRef } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  ScrollView,
  Modal,
  Alert,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { router } from 'expo-router';
import {
  Zap,
  Users,
  Package,
  BarChart3,
  Sparkles,
  TrendingUp,
  Shield,
  X,
} from 'lucide-react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withRepeat,
  withSequence,
  withTiming,
  interpolate,
  withDelay,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import Colors from '@/constants/colors';
import { commonStyles } from '@/styles/commonStyles';
import { typography, spacing, radius } from '@/constants/theme';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import InventoryReports from '@/components/reports/InventoryReports';
import { useServiceDraftStore } from '@/stores/service-draft-store';
import { useDashboardStore } from '@/stores/dashboard-store';
import { useAnimationsEnabled, useHapticsEnabled, useWhimsyStore } from '@/stores/whimsy-store';

// Animated components
const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);
const AnimatedView = Animated.createAnimatedComponent(View);

// Animated logo with easter egg
const AnimatedLogo: React.FC = () => {
  const rotation = useSharedValue(0);
  const scale = useSharedValue(1);
  const rainbow = useSharedValue(0);
  const [easterEggCount, setEasterEggCount] = useState(0);
  const [showEasterEgg, setShowEasterEgg] = useState(false);
  const lastTap = useRef(0);
  const hapticsEnabled = useHapticsEnabled();
  const { incrementMetric } = useWhimsyStore.getState();
  const easterEggsEnabled = useWhimsyStore(state => state.enableEasterEggs);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${rotation.value}deg` }, { scale: scale.value }],
  }));

  const rainbowStyle = useAnimatedStyle(() => ({
    opacity: interpolate(rainbow.value, [0, 1], [0, 0.3]),
  }));

  const handleLogoPress = () => {
    const now = Date.now();
    if (now - lastTap.current < 500) {
      // Double tap within 500ms
      setEasterEggCount(prev => prev + 1);

      // Easter egg activation after 5 taps (only if enabled)
      if (easterEggCount >= 4 && easterEggsEnabled) {
        setShowEasterEgg(true);
        if (hapticsEnabled) {
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        }

        // Track easter egg discovery
        incrementMetric('easterEggsFound');

        // Rainbow + spin animation
        rainbow.value = withRepeat(withTiming(1, { duration: 200 }), 10, true);
        rotation.value = withSequence(
          withTiming(360, { duration: 1000 }),
          withTiming(720, { duration: 500 })
        );

        Alert.alert(
          '🎨 ¡Salonier Easter Egg!',
          '¡Has desbloqueado el Modo Colorista Experto!\n\nEquipo: Oscar Cortijo & Claude AI\nVersión: 2.1.0\n\n✨ ¡Sigue creando belleza!',
          [{ text: '¡Genial!', onPress: () => setEasterEggCount(0) }]
        );

        setTimeout(() => {
          setShowEasterEgg(false);
          rainbow.value = withTiming(0, { duration: 1000 });
        }, 3000);
      } else {
        // Small bounce animation
        scale.value = withSequence(withSpring(1.1, { damping: 10 }), withSpring(1, { damping: 8 }));
        if (hapticsEnabled) {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        }
      }
    }

    lastTap.current = now;
  };

  return (
    <TouchableOpacity onPress={handleLogoPress} activeOpacity={0.8}>
      <View style={commonStyles.positionRelative}>
        {/* Rainbow background for easter egg */}
        <Animated.View style={[styles.rainbowBg, rainbowStyle]} />

        <Animated.Text style={[styles.title, animatedStyle]}>
          Salonier {showEasterEgg && '✨'}
        </Animated.Text>

        {/* Progress indicator for easter egg */}
        {easterEggCount > 0 && easterEggCount < 5 && (
          <View style={styles.easterEggProgress}>
            {[...Array(5)].map((_, i) => (
              <View
                key={i}
                style={[
                  styles.easterEggDot,
                  {
                    backgroundColor:
                      i < easterEggCount ? Colors.light.primary : Colors.light.border,
                  },
                ]}
              />
            ))}
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

// Breathing card component
const BreathingCard: React.FC<{
  children: React.ReactNode;
  onPress?: () => void;
  delay?: number;
}> = ({ children, onPress, delay = 0 }) => {
  const breatheScale = useSharedValue(1);
  const pressScale = useSharedValue(1);
  const animationsEnabled = useAnimationsEnabled();
  const hapticsEnabled = useHapticsEnabled();

  useEffect(() => {
    // Only animate if enabled
    if (animationsEnabled) {
      breatheScale.value = withDelay(
        delay,
        withRepeat(
          withSequence(withTiming(1.02, { duration: 2000 }), withTiming(1, { duration: 2000 })),
          -1,
          true
        )
      );
    }
  }, [delay, animationsEnabled, breatheScale]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: breatheScale.value * pressScale.value }],
  }));

  const handlePressIn = () => {
    pressScale.value = withSpring(0.98);
  };

  const handlePressOut = () => {
    pressScale.value = withSpring(1);
  };

  const handlePress = () => {
    if (hapticsEnabled) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    onPress?.();
  };

  if (onPress) {
    return (
      <AnimatedTouchableOpacity
        style={animatedStyle}
        onPress={handlePress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={1}
      >
        {children}
      </AnimatedTouchableOpacity>
    );
  }

  return <AnimatedView style={animatedStyle}>{children}</AnimatedView>;
};

export default function HomeScreen() {
  const [showReportsModal, setShowReportsModal] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { configuration } = useSalonConfigStore();
  const { getPendingDrafts, deleteDraft } = useServiceDraftStore();
  const { metrics, isLoading, loadTodayMetrics, startAutoRefresh } = useDashboardStore();

  const handleProductSelect = (productId: string) => {
    setShowReportsModal(false);
    router.push(`/inventory/${productId}`);
  };

  // Check for pending service drafts on mount
  useEffect(() => {
    const checkPendingDrafts = async () => {
      const drafts = getPendingDrafts();

      if (drafts.length > 0) {
        // Show the most recent draft
        const mostRecentDraft = drafts.sort(
          (a, b) => new Date(b.lastSaved).getTime() - new Date(a.lastSaved).getTime()
        )[0];

        const timeAgo = getTimeAgo(mostRecentDraft.lastSaved);

        Alert.alert(
          'Servicio pendiente',
          `Tienes un servicio sin terminar para ${mostRecentDraft.clientName} (guardado ${timeAgo})`,
          [
            {
              text: 'Descartar',
              style: 'destructive',
              onPress: () => {
                deleteDraft(mostRecentDraft.id);
                // Draft discarded - no logging needed in production
              },
            },
            {
              text: 'Continuar',
              onPress: () => {
                // Navigate to service with client and restore draft
                router.push({
                  pathname: '/service/new',
                  params: {
                    clientId: mostRecentDraft.clientId,
                    restoreDraft: 'true',
                  },
                });
              },
            },
          ],
          { cancelable: true }
        );
      }
    };

    // Delay check slightly to avoid immediate alerts
    const timer = setTimeout(checkPendingDrafts, 500);
    return () => clearTimeout(timer);
  }, [deleteDraft, getPendingDrafts]);

  // Load dashboard metrics on mount and set up auto-refresh
  useEffect(() => {
    loadTodayMetrics();
    const cleanup = startAutoRefresh();
    return cleanup;
  }, [loadTodayMetrics, startAutoRefresh]);

  // Custom refresh handler
  const handleRefresh = async () => {
    setIsRefreshing(true);
    const hapticsEnabled = useWhimsyStore.getState().enableHapticFeedback;

    try {
      await loadTodayMetrics();
      if (hapticsEnabled) {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }
    } finally {
      setTimeout(() => setIsRefreshing(false), 800); // Keep spinner a bit longer for better UX
    }
  };

  const getTimeAgo = (date: Date) => {
    const now = new Date();
    const saved = new Date(date);
    const diffMs = now.getTime() - saved.getTime();
    const diffSecs = Math.floor(diffMs / 1000);
    const diffMins = Math.floor(diffMs / 60000);

    if (diffSecs < 60) {
      return `hace unos segundos`;
    } else if (diffMins < 60) {
      return `hace ${diffMins} minuto${diffMins === 1 ? '' : 's'}`;
    } else if (diffMins < 1440) {
      const hours = Math.floor(diffMins / 60);
      return `hace ${hours} hora${hours > 1 ? 's' : ''}`;
    } else {
      const days = Math.floor(diffMins / 1440);
      return `hace ${days} día${days > 1 ? 's' : ''}`;
    }
  };

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl
          refreshing={isRefreshing}
          onRefresh={handleRefresh}
          tintColor={Colors.light.primary}
          colors={[Colors.light.primary]}
          progressBackgroundColor="white"
        />
      }
      showsVerticalScrollIndicator={false}
    >
      <View style={styles.header}>
        <AnimatedLogo />
        <Text style={styles.subtitle}>Asistente de Coloración Profesional</Text>
      </View>

      <View style={styles.quickActions}>
        <Text style={styles.sectionTitle}>Acciones Rápidas</Text>

        <BreathingCard onPress={() => router.push('/service/client-selection')}>
          <LinearGradient
            colors={[Colors.light.primary, Colors.light.secondary]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.primaryAction}
          >
            <View style={styles.actionIcon}>
              <Zap size={28} color="white" />
            </View>
            <View style={styles.actionContent}>
              <Text style={styles.actionTitle}>Nuevo Servicio</Text>
              <Text style={styles.actionSubtitle}>
                Diagnóstico capilar inteligente con selección de cliente
              </Text>
            </View>
            <Sparkles size={24} color="white" />
          </LinearGradient>
        </BreathingCard>

        <View style={styles.secondaryActions}>
          <BreathingCard onPress={() => router.push('/clients')} delay={100}>
            <View style={styles.secondaryAction}>
              <Users size={24} color={Colors.light.primary} />
              <Text style={styles.secondaryActionText}>Clientes</Text>
            </View>
          </BreathingCard>

          <BreathingCard onPress={() => router.push('/inventory')} delay={200}>
            <View style={styles.secondaryAction}>
              <Package size={24} color={Colors.light.primary} />
              <Text style={styles.secondaryActionText}>Inventario</Text>
            </View>
          </BreathingCard>

          {configuration.inventoryControlLevel !== 'solo-formulas' && (
            <BreathingCard onPress={() => setShowReportsModal(true)} delay={300}>
              <View style={styles.secondaryAction}>
                <BarChart3 size={24} color={Colors.light.primary} />
                <Text style={styles.secondaryActionText}>Reportes</Text>
              </View>
            </BreathingCard>
          )}
        </View>
      </View>

      <View style={styles.statsSection}>
        <Text style={styles.sectionTitle}>Resumen de Hoy</Text>

        <View style={styles.statsGrid}>
          <BreathingCard onPress={() => router.push('/clients')} delay={100}>
            <View style={styles.statCard}>
              <View style={styles.statIcon}>
                <Zap size={20} color={Colors.light.primary} />
              </View>
              {isLoading ? (
                <ActivityIndicator
                  size="small"
                  color={Colors.light.primary}
                  style={commonStyles.marginVertical8}
                />
              ) : (
                <Text style={styles.statNumber}>{metrics.servicestoday}</Text>
              )}
              <Text style={styles.statLabel}>Servicios Hoy</Text>
            </View>
          </BreathingCard>

          <BreathingCard onPress={() => router.push('/clients')} delay={200}>
            <View style={styles.statCard}>
              <View style={styles.statIcon}>
                <Users size={20} color={Colors.light.primary} />
              </View>
              {isLoading ? (
                <ActivityIndicator
                  size="small"
                  color={Colors.light.primary}
                  style={commonStyles.marginVertical8}
                />
              ) : (
                <Text style={styles.statNumber}>{metrics.totalClients}</Text>
              )}
              <Text style={styles.statLabel}>Clientes</Text>
            </View>
          </BreathingCard>

          <BreathingCard onPress={() => setShowReportsModal(true)} delay={300}>
            <View style={styles.statCard}>
              <View style={styles.statIcon}>
                <TrendingUp size={20} color={Colors.light.success} />
              </View>
              {isLoading ? (
                <ActivityIndicator
                  size="small"
                  color={Colors.light.primary}
                  style={commonStyles.marginVertical8}
                />
              ) : (
                <Text style={styles.statNumber}>
                  {metrics.averageSatisfaction > 0
                    ? `${Math.round(metrics.averageSatisfaction * 20)}%`
                    : '-'}
                </Text>
              )}
              <Text style={styles.statLabel}>Satisfacción</Text>
            </View>
          </BreathingCard>

          <BreathingCard delay={400}>
            <View style={styles.statCard}>
              <View style={styles.statIcon}>
                <Shield size={20} color={Colors.light.secondary} />
              </View>
              <View style={styles.privacyIndicator}>
                <Text style={styles.statNumber}>🔒</Text>
                <Text style={styles.privacyStatus}>Protegida</Text>
              </View>
              <Text style={styles.statLabel}>Privacidad</Text>
            </View>
          </BreathingCard>
        </View>
      </View>

      {/* Feedback Stats Section */}
      <View style={styles.statsSection}>
        <Text style={styles.sectionTitle}>Sistema de Feedback</Text>
        <View style={[styles.card, styles.feedbackCard]}>
          <Text style={[styles.cardTitle, styles.feedbackTitle]}>📊 Estadísticas de Feedback</Text>
          <Text style={styles.feedbackDescription}>
            Aquí verás el rendimiento de tus fórmulas y la satisfacción de tus clientes
            una vez que comiences a usar el sistema.
          </Text>
        </View>
      </View>

      <View style={styles.featuresSection}>
        <Text style={styles.sectionTitle}>Características Destacadas</Text>

        <View style={styles.featureCard}>
          <View style={styles.featureIcon}>
            <Zap size={24} color={Colors.light.primary} />
          </View>
          <View style={styles.featureContent}>
            <Text style={styles.featureTitle}>Análisis IA Profesional</Text>
            <Text style={styles.featureDescription}>
              Diagnóstico capilar avanzado con reconocimiento de imagen y recomendaciones
              personalizadas
            </Text>
          </View>
        </View>

        <View style={styles.featureCard}>
          <View style={styles.featureIcon}>
            <Shield size={24} color={Colors.light.success} />
          </View>
          <View style={styles.featureContent}>
            <Text style={styles.featureTitle}>Privacidad Total</Text>
            <Text style={styles.featureDescription}>
              Política &quot;Analizar y Descartar&quot; - Las imágenes se procesan y eliminan
              inmediatamente
            </Text>
          </View>
        </View>

        <View style={styles.featureCard}>
          <View style={styles.featureIcon}>
            <Users size={24} color={Colors.light.secondary} />
          </View>
          <View style={styles.featureContent}>
            <Text style={styles.featureTitle}>Historial Inteligente</Text>
            <Text style={styles.featureDescription}>
              Seguimiento completo de clientes con alertas de alergias y recomendaciones
              personalizadas
            </Text>
          </View>
        </View>
      </View>

      {/* Reports Modal */}
      <Modal visible={showReportsModal} animationType="slide" presentationStyle="pageSheet">
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowReportsModal(false)}>
              <X size={24} color={Colors.light.gray} />
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Reportes de Inventario</Text>
            <View style={commonStyles.width24} />
          </View>

          <InventoryReports onProductSelect={handleProductSelect} />
        </View>
      </Modal>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  header: {
    padding: spacing.lg,
    paddingTop: 40,
    backgroundColor: Colors.light.background,
  },
  title: {
    fontSize: typography.sizes['4xl'],
    fontWeight: typography.weights.extrabold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  rainbowBg: {
    position: 'absolute',
    top: -4,
    left: -8,
    right: -8,
    bottom: -4,
    borderRadius: 12,
    backgroundColor: Colors.light.highlight,
  },
  easterEggProgress: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 4,
    gap: 4,
  },
  easterEggDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },
  subtitle: {
    fontSize: typography.sizes.base,
    color: Colors.light.textSecondary,
    fontWeight: typography.weights.medium,
  },
  quickActions: {
    padding: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
    marginBottom: spacing.md,
  },
  primaryAction: {
    borderRadius: radius.lg,
    padding: spacing.lg,
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 4,
  },
  actionIcon: {
    width: 56,
    height: 56,
    borderRadius: radius.full,
    backgroundColor: Colors.light.primaryTransparent20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  actionContent: {
    flex: 1,
  },
  actionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: Colors.light.textLight,
    marginBottom: spacing.xs,
  },
  actionSubtitle: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textLight,
    lineHeight: 20,
  },
  secondaryActions: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  secondaryAction: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: Colors.light.card,
    padding: spacing.md,
    borderRadius: radius.lg,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  secondaryActionText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginTop: spacing.sm,
  },
  statsSection: {
    padding: spacing.lg,
    paddingTop: 0,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  statCard: {
    flex: 1,
    minWidth: '45%',
    alignItems: 'center',
    padding: spacing.md,
    backgroundColor: Colors.light.card,
    borderRadius: radius.lg,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  statIcon: {
    width: 40,
    height: 40,
    borderRadius: radius.full,
    backgroundColor: Colors.light.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  statNumber: {
    fontSize: typography.sizes['2xl'],
    fontWeight: typography.weights.extrabold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  statLabel: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
    fontWeight: typography.weights.medium,
  },
  featuresSection: {
    padding: spacing.lg,
    paddingTop: 0,
  },
  featureCard: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
    padding: spacing.md,
    backgroundColor: Colors.light.card,
    borderRadius: radius.lg,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  featureIcon: {
    width: 48,
    height: 48,
    borderRadius: radius.full,
    backgroundColor: Colors.light.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  featureContent: {
    flex: 1,
  },
  featureTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  featureDescription: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    lineHeight: 20,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: Colors.light.card,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  modalTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
  feedbackCard: {
    padding: 16,
    backgroundColor: '#f8f9fa',
  },
  feedbackTitle: {
    color: '#333',
  },
  feedbackDescription: {
    color: '#666',
    marginTop: 8,
  },
  privacyIndicator: {
    alignItems: 'center',
  },
  privacyStatus: {
    fontSize: typography.sizes.xs,
    color: Colors.light.success,
    fontWeight: typography.weights.medium,
    marginTop: 2,
  },
});

import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Clock, MapPin, Info, ChevronDown, ChevronUp, Beaker } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { FormulationStep } from '@/types/formulation';

interface StepDetailCardProps {
  step: FormulationStep;
  stepNumber: number;
  totalSteps: number;
  isActive?: boolean;
  onToggle?: () => void;
}

export const StepDetailCard: React.FC<StepDetailCardProps> = ({
  step,
  stepNumber,
  totalSteps,
  isActive = false,
  onToggle,
}) => {
  const [isExpanded, setIsExpanded] = useState(isActive);

  // Ensure step has required properties
  if (!step || typeof step !== 'object') {
    return (
      <View style={[styles.container, styles.errorContainer]}>
        <Text style={styles.errorText}>
          Error: Paso inválido {stepNumber} de {totalSteps}
        </Text>
      </View>
    );
  }

  const handleToggle = () => {
    setIsExpanded(!isExpanded);
    onToggle?.();
  };

  // Get color for step type
  const getStepColor = () => {
    const title = (step.stepTitle || '').toLowerCase();
    if (title.includes('decolor') || title.includes('aclaración')) return Colors.light.warning;
    if (title.includes('matiz') || title.includes('tono')) return Colors.light.primary;
    if (title.includes('tratamiento')) return Colors.light.success;
    return Colors.light.accent;
  };

  const stepColor = getStepColor();

  return (
    <View style={[styles.container, isActive && styles.activeContainer]}>
      <TouchableOpacity style={styles.header} onPress={handleToggle} activeOpacity={0.7}>
        <View style={styles.headerLeft}>
          <View style={[styles.stepBadge, { backgroundColor: stepColor + '20' }]}>
            <Text style={[styles.stepBadgeText, { color: stepColor }]}>{stepNumber}</Text>
          </View>
          <View style={styles.headerText}>
            <Text style={styles.stepTitle}>{step.stepTitle || `Paso ${stepNumber}`}</Text>
            <Text style={styles.stepSubtitle}>
              Paso {stepNumber} de {totalSteps}
            </Text>
          </View>
        </View>
        {isExpanded ? (
          <ChevronUp size={20} color={Colors.light.gray} />
        ) : (
          <ChevronDown size={20} color={Colors.light.gray} />
        )}
      </TouchableOpacity>

      {isExpanded && (
        <View style={styles.content}>
          {/* Formula Section */}
          {step.mix && step.mix.length > 0 && (
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <Beaker size={16} color={stepColor} />
                <Text style={styles.sectionTitle}>Fórmula</Text>
              </View>
              <View style={styles.mixContainer}>
                {step.mix.map((product, index) => (
                  <View key={index} style={styles.productItem}>
                    <Text style={styles.productName}>{product.productName || 'Producto'}</Text>
                    <Text style={styles.productQuantity}>
                      {(product.quantity || 0) + (product.unit || 'gr')}
                    </Text>
                  </View>
                ))}
              </View>
            </View>
          )}

          {/* Instructions Section */}
          {step.instructions && (
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <Info size={16} color={stepColor} />
                <Text style={styles.sectionTitle}>Instrucciones</Text>
              </View>
              <Text style={styles.instructions}>
                {step.instructions || 'Sin instrucciones específicas'}
              </Text>
            </View>
          )}

          {/* Technique Section */}
          {step.technique && step.technique.name && (
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <MapPin size={16} color={stepColor} />
                <Text style={styles.sectionTitle}>
                  Técnica: {step.technique.name || 'No especificada'}
                </Text>
              </View>
              {step.technique.description && (
                <Text style={styles.techniqueDescription}>{step.technique.description}</Text>
              )}
            </View>
          )}

          {/* Processing Time */}
          {step.processingTime && (
            <View style={styles.timeContainer}>
              <Clock size={16} color={stepColor} />
              <Text style={styles.timeText}>
                Tiempo de procesamiento: {step.processingTime || 0} minutos
              </Text>
            </View>
          )}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.light.background,
    borderRadius: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: Colors.light.border,
    overflow: 'hidden',
  },
  activeContainer: {
    borderColor: Colors.light.primary,
    borderWidth: 2,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  stepBadge: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  stepBadgeText: {
    fontSize: 18,
    fontWeight: '700',
  },
  headerText: {
    marginLeft: 12,
    flex: 1,
  },
  stepTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
  },
  stepSubtitle: {
    fontSize: 13,
    color: Colors.light.gray,
    marginTop: 2,
  },
  content: {
    padding: 16,
    paddingTop: 0,
  },
  section: {
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
  },
  mixContainer: {
    backgroundColor: Colors.light.surface,
    borderRadius: 12,
    padding: 12,
  },
  productItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  productName: {
    fontSize: 14,
    color: Colors.light.text,
    flex: 1,
  },
  productQuantity: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
    marginLeft: 12,
  },
  instructions: {
    fontSize: 14,
    color: Colors.light.text,
    lineHeight: 20,
  },
  techniqueDescription: {
    fontSize: 14,
    color: Colors.light.gray,
    lineHeight: 20,
    fontStyle: 'italic',
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.primary + '10',
    borderRadius: 8,
    padding: 12,
    gap: 8,
  },
  timeText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.light.primary,
  },
  errorContainer: {
    borderColor: Colors.light.error,
    backgroundColor: Colors.light.error + '10',
  },
  errorText: {
    fontSize: 14,
    color: Colors.light.error,
    textAlign: 'center',
    padding: 16,
  },
});
